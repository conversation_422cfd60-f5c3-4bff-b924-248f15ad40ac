package org.example.back.service

import org.example.back.entity.User
import org.example.back.repository.UserRepository
import org.springframework.stereotype.Service

@Service
class UserService(private val userRepository: UserRepository) {
    fun getUsers(): List<User> {
        return userRepository.findAll()
    }

    fun getUserByEmail(email: String): User? {
        return userRepository.findByEmail(email)
    }

    fun createUserIfNotExists(name: String?, email: String?, amazonUserId: String) {
        if (email == null) {
            return
        }
        if (!userRepository.existsByEmail(email)) {
            userRepository.save(User(
                email = email,
                firstName = name ?: "Unknown",
                lastName = "User",
                createdAt = java.time.LocalDateTime.now(),
                updatedAt = java.time.LocalDateTime.now()
            ))
        }
    }
}