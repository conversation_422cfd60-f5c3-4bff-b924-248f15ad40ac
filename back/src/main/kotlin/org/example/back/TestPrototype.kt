package org.example.back

import jakarta.annotation.PostConstruct
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

@Component
@Scope("prototype")
class TestPrototype {
    val id = System.currentTimeMillis()
    @PostConstruct
    fun init() {
        println("TestPrototype created with id $id")
    }

    override fun toString(): String {
        return "TestPrototype(id=$id)"
    }
}