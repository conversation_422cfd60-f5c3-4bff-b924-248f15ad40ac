package org.example.back

import org.springframework.beans.factory.ObjectProvider
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class Scheduler {
    private var singleton: ObjectProvider<TestSingleton>

    constructor(singleton: ObjectProvider<TestSingleton>) {
        this.singleton = singleton
    }

    @Scheduled(fixedRate = 2000)
    fun scheduledTask() {
        singleton.getIfAvailable()?.let {
            println("Singleton info after update: ${it.getData()}")
        }
    }
}