package org.example.back

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.springframework.beans.factory.annotation.Lookup
import org.springframework.stereotype.Component

@Component
class TestSingleton {
    private var prototype: TestPrototype? = null

    constructor(prototype: TestPrototype) {
        this.prototype = prototype
    }

    init {
        println("TestSingleton instance created: ${this.hashCode()}")
    }

    @Lookup
    fun getPrototype(): TestPrototype? {
        return null
    }

    fun getData(): String {
        return getPrototype()?.toString() ?: "Prototype is null"
    }
}
