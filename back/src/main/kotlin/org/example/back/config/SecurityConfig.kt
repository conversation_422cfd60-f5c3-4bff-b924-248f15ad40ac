package org.example.back.config

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.example.back.service.UserService
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.core.Authentication
import org.springframework.security.oauth2.core.user.OAuth2User
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AuthenticationSuccessHandler
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.util.UriComponentsBuilder

@Configuration
@EnableWebSecurity
class SecurityConfig(
    private val corsConfigurationSource: CorsConfigurationSource,
    private val userService: UserService,
){
    @Value("\${frontend.url}")
    private lateinit var frontendUrl: String


    @Bean
    @Throws(Exception::class)
    fun filterChain(http: HttpSecurity,): SecurityFilterChain {
        return http
            .csrf { it.disable() }
            .cors{ cors ->
                cors.configurationSource(corsConfigurationSource)
            }
            .authorizeHttpRequests { auth ->
                auth
                    .requestMatchers("/h2-console/**").permitAll()
                    .requestMatchers("/api/**").permitAll()
                    .anyRequest().authenticated()
            }
            .oauth2Login { oauth2 ->
                oauth2.successHandler(authenticationSuccessHandler())
            }
            .build()
    }

    @Bean
    fun authenticationSuccessHandler(): AuthenticationSuccessHandler {
        return AuthenticationSuccessHandler { _, response, authentication ->
            val oauthUser = authentication.principal as OAuth2User

            val name: String? = oauthUser.getAttribute("name")
            val email: String? = oauthUser.getAttribute("email")
            val amazonUserId: String = oauthUser.name

            userService.createUserIfNotExists(name, email, amazonUserId)

            val appJwt = "fake-jwt-for-$email"

            val redirectUrl = UriComponentsBuilder.fromUriString("$frontendUrl/login/callback")
                .queryParam("token", appJwt)
                .build().toUriString()

            response.sendRedirect(redirectUrl)
        }
    }
}