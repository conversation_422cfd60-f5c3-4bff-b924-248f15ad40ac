import {Box, Button, Container, Input} from "@mui/material";
import {useForm} from "react-hook-form";
import styles from "./registration-form.module.css";

export default function RegistrationForm() {
    const {register, handleSubmit} = useForm();

    return <Container className={styles.centered}>
        <form onSubmit={handleSubmit(
            (data: any) => {
                console.log(data);
            },
            (errors) => {
                console.log(errors);
                if (errors.firstName || errors.lastName) {
                    alert("First and last name are required");
                }
            }
        )}>

            <Box sx={{display: 'flex', flexDirection: 'column', gap: 2, maxWidth: 400}}>
                <Input {...register("firstName", {required: true})} placeholder="First Name"/>
                <Input {...register("lastName", {required: true})} placeholder="Last Name"/>
                <Input {...register("email")} type="email" placeholder="Email"/>
                <Button variant="contained" type="submit">Register</Button>
                <a href={import.meta.env.VITE_API + '/oauth2/authorization/amazon'}>
                    <img src="/amazon.png" alt="Log in with Amazon"/>
                </a>
            </Box>
        </form>
    </Container>;
}