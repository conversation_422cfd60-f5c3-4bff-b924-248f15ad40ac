import {Link as RouterLink} from "react-router-dom";
import {Chip} from "@mui/material";
import styled from "styled-components";

const StyledNav = styled.div`
    display: flex;
    align-items: center;
    padding: 10px;
    gap: 10px;
`;

export default function Navbar(){
    return (
        <div>
            <StyledNav>
                <RouterLink to="/"><Chip label="Home" variant="outlined" /></RouterLink>
                <RouterLink to="/users"><Chip label="Users" variant="outlined" /></RouterLink>
                <RouterLink to="/register"><Chip label="Register" variant="outlined" /></RouterLink>
            </StyledNav>

            <hr />
        </div>
    )
}