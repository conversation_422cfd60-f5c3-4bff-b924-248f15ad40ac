import type ApiResponse from "../../dtos/ApiResponse.ts";
import {<PERSON><PERSON>, <PERSON>} from "@mui/material";
import {useQuery} from "@tanstack/react-query";

export default function Home (){
    const baseApi = import.meta.env.VITE_API;

    async function fetchMe(): Promise<unknown> {
        const response = await fetch(baseApi + 'users/me');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: ApiResponse = await response.json();
        console.log(data);
        return data._embedded;
    }

    const {
        data: me,
        isLoading,
    } = useQuery({
        queryKey: ['me'],
        queryFn: fetchMe,
        staleTime: 1000 * 60 * 5,
    })

    if (isLoading) {
        return <div>Losading...</div>;
    }

    return (
        <div>
            <h1>Home</h1>
            <Card variant="outlined">
                <h1>{me.firstName} {me.lastName}</h1>
                <p>{me.email}</p>
            </Card>
        </div>
    )
}