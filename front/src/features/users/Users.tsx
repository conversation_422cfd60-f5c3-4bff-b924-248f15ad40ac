import { useQuery } from '@tanstack/react-query';
import {
    Alert,
    Box,
    CircularProgress,
    Container,
    Typography,
    Accordion,
    AccordionSummary,
    AccordionDetails
} from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import type ApiResponse from "../../dtos/ApiResponse.ts";
import type User from "../../dtos/User.ts";

const baseApi = import.meta.env.VITE_API;

const loadingContainerStyles = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '200px'
};

const loadingTextStyles = {
    ml: 2
};

const titleStyles = {
    mt: 4,
    mb: 3
};

const errorStyles = {
    mt: 2
};

async function fetchUsers(): Promise<User[]> {
    const response = await fetch(baseApi + 'users');

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();
    console.log(data);
    return data._embedded;
}

export default function Users() {
    const {
        data: users = [],
        isLoading,
        error
    } = useQuery<User[]>({
        queryKey: ['users'],
        queryFn: fetchUsers,
        staleTime: 1000 * 60 * 5,
    });

    if (isLoading) {
        return (
            <Container maxWidth="sm">
                <Box sx={loadingContainerStyles}>
                    <CircularProgress />
                    <Typography variant="h6" sx={loadingTextStyles}>
                        Loading users...
                    </Typography>
                </Box>
            </Container>
        );
    }

    if (error) {
        return (
            <Container maxWidth="sm">
                <Alert severity="error" sx={errorStyles}>
                    {error.message}
                </Alert>
            </Container>
        );
    }

    const groupedUsers = users.reduce((acc: Record<string, User[]>, user: User) => {
        const nameKey = `${user.firstName} ${user.lastName}`;
        (acc[nameKey] ??= []).push(user);
        return acc;
    }, {});

    const userGroups: [string, User[]][] = Object.entries(groupedUsers);

    return (
        <Container maxWidth="md">
            <Typography variant="h4" component="h1" gutterBottom sx={titleStyles}>
                Users ({userGroups.length} unique names, {users.length} total)
            </Typography>
            {userGroups.map(([fullName, userList]) => (
                <Accordion key={fullName} sx={{ mb: 1 }}>
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls={`user-${fullName}-content`}
                        id={`user-${fullName}-header`}
                    >
                        <Typography variant="h6" component="h2">
                            {fullName} {userList.length > 1 && `(${userList.length} entries)`}
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{ p: 0 }}>
                        {userList.map((user, index) => (
                            <Box key={user.id} sx={{ p: 2, borderBottom: index < userList.length - 1 ? '1px solid' : 'none', borderColor: 'divider' }}>
                                <Typography variant="body2" color="text.secondary">
                                    <strong>ID:</strong> {user.id} | <strong>First Name:</strong> {user.firstName} | <strong>Last Name:</strong> {user.lastName}{user.email ? ` | <strong>Email:</strong> ${user.email}` : ''}
                                </Typography>
                            </Box>
                        ))}
                    </AccordionDetails>
                </Accordion>
            ))}
        </Container>
    );
}