// Simple Users component tests for learning purposes
describe('Users Component Testing', () => {
    let mockFetch: jasmine.Spy;
    let originalFetch: typeof fetch;

    beforeEach(() => {
        // Store the original fetch function
        originalFetch = window.fetch;
        // Create a mock fetch function
        mockFetch = jasmine.createSpy('fetch');
        window.fetch = mockFetch;
    });

    afterEach(() => {
        // Restore the original fetch function
        window.fetch = originalFetch;
    });

    it('should mock fetch API calls', async () => {
        const mockResponse = {
            ok: true,
            json: jasmine.createSpy('json').and.returnValue(Promise.resolve({
                _embedded: {
                    users: [
                        { id: 1, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
                        { id: 2, firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' }
                    ]
                }
            }))
        };

        mockFetch.and.returnValue(Promise.resolve(mockResponse));

        const response = await fetch('http://localhost:8080/api/users');
        const data = await response.json();
        expect(data._embedded.users.length).toBe(2);
        expect(data._embedded.users[0].firstName).toBe('<PERSON>');

        expect(mockFetch).toHaveBeenCalledWith('http://localhost:8080/api/users');
    });

    it('should handle API errors', async () => {
        // Mock a failed API call
        mockFetch.and.returnValue(Promise.reject(new Error('Network error')));

        try {
            await fetch('http://localhost:8080/api/users');
        } catch (error: unknown) {
            expect((error as Error).message).toBe('Network error');
        }

        expect(mockFetch).toHaveBeenCalled();
    });

    it('should test array filtering (like distinct users)', () => {
        // Test the logic your component uses for distinct users
        const users = [
            { id: 1, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            { id: 2, firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
            { id: 3, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' } // Duplicate name
        ];

        const distinctUsers = users.filter((user, index, self) =>
            index === self.findIndex(u => u.firstName + u.lastName === user.firstName + user.lastName)
        );

        expect(distinctUsers.length).toBe(2);
        expect(distinctUsers[0].firstName).toBe('John');
        expect(distinctUsers[1].firstName).toBe('Jane');
    });
});
