import { createTheme } from '@mui/material/styles';

// Define your custom color palette
const theme = createTheme({
  palette: {
    primary: {
      main: '#253f5e',      // Main primary color
      light: '#4877ba',     // Light variant
      dark: '#1565c0',      // Dark variant
      contrastText: '#ffffff', // Text color on primary background
    },
    secondary: {
      main: '#d51616',      // Main secondary color
      light: '#ff5983',     // Light variant
      dark: '#9a0036',      // Dark variant
      contrastText: '#fff', // Text color on secondary background
    },
    success: {
      main: '#2e7d32',
      light: '#4caf50',
      dark: '#1b5e20',
    },
    warning: {
      main: '#ed6c02',
      light: '#ff9800',
      dark: '#e65100',
    },
    error: {
      main: '#d32f2f',
      light: '#ef5350',
      dark: '#c62828',
    },
    info: {
      main: '#50ca35',
      light: '#76fa79',
      dark: '#50ca35',
    },
    background: {
      default: '#1d2025',   // Page background
      paper: '#1f2023',     // Card/paper background
    },
    text: {
      primary: '#4295c8',   // Primary text color
      secondary: '#757575', // Secondary text color
    },
    grey: {
      50: '#f9f9f9',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121',
    },
  },
  // You can also customize typography, spacing, etc.
  typography: {
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8, // Rounded corners for cards, buttons, etc.
  },
});

export default theme;
