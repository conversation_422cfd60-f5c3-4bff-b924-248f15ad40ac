import './App.css'
import Navbar from "./features/navbar/navbar.tsx";
import {Route, Routes} from "react-router-dom";
import Home from "./features/home/<USER>";
import Users from "./features/users/Users.tsx";
import RegistrationForm from "./features/registration-form/registration-form.tsx";

function App() {

    return (
        <div className="app">
            <Navbar/>
            <Routes>
                <Route path="/" element={<Home/>}/>
                <Route path="/users" element={<Users/>}/>
                <Route path="/register" element={<RegistrationForm/>}/>
            </Routes>
        </div>
    )
}

export default App
